// Test 2: Playwright with comprehensive evasion techniques
const { chromium } = require('playwright');

(async () => {
  console.log('Test 2: Playwright with comprehensive evasion');
  
  const browser = await chromium.launch({
    headless: false,
    args: [
      '--disable-blink-features=AutomationControlled',
      '--disable-web-security',
      '--disable-features=IsolateOrigins,site-per-process',
      '--disable-setuid-sandbox',
      '--no-sandbox',
      '--disable-gpu',
      '--disable-dev-shm-usage'
    ]
  });
  
  const context = await browser.newContext({
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport: { width: 1920, height: 1080 },
    locale: 'en-US',
    timezoneId: 'America/New_York',
    permissions: ['geolocation', 'notifications'],
    deviceScaleFactor: 1,
    hasTouch: false,
    isMobile: false
  });
  
  const page = await context.newPage();
  
  // Remove webdriver and other automation indicators
  await page.addInitScript(() => {
    // Remove webdriver
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined
    });
    
    // Mock plugins
    Object.defineProperty(navigator, 'plugins', {
      get: () => [1, 2, 3, 4, 5]
    });
    
    // Mock languages
    Object.defineProperty(navigator, 'languages', {
      get: () => ['en-US', 'en']
    });
    
    // Mock permissions
    const originalQuery = window.navigator.permissions.query;
    window.navigator.permissions.query = (parameters) => (
      parameters.name === 'notifications' ?
        Promise.resolve({ state: Notification.permission }) :
        originalQuery(parameters)
    );
    
    // Mock chrome object
    window.chrome = {
      runtime: {},
      loadTimes: function() {},
      csi: function() {},
      app: {}
    };
  });
  
  try {
    await page.goto('https://player.vimeo.com/video/887967850?autoplay=1#t=10s');
    await page.waitForTimeout(5000);
    
    // Take screenshot
    await page.screenshot({ path: 'test2-result.png' });
    console.log('Screenshot saved as test2-result.png');
    
    // Check if we got blocked
    const title = await page.title();
    const content = await page.content();
    
    if (content.includes('security') || content.includes('blocked')) {
      console.log('❌ Test 2 FAILED - Security verification detected');
    } else {
      console.log('✅ Test 2 PASSED - No security block detected');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await browser.close();
  }
})();