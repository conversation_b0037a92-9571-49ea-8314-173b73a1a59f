{"name": "playwright-evasion-tests", "version": "1.0.0", "description": "Test scripts for Playwright evasion techniques", "scripts": {"test1": "node test1-basic.js", "test2": "node test2-stealth.js", "test3": "node test3-extra-stealth.js", "test-all": "npm run test1 && npm run test2 && npm run test3"}, "dependencies": {"playwright": "^1.40.0"}, "optionalDependencies": {"playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2"}}