// Test 4: Exact configuration from working browser
const { chromium } = require('playwright');

(async () => {
  console.log('Test 4: Exact browser configuration replication');
  
  const browser = await chromium.launch({
    headless: false,
    args: [
      '--disable-blink-features=AutomationControlled',
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-infobars',
      '--window-size=1705,844',
      '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ]
  });
  
  const context = await browser.newContext({
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    viewport: { width: 1705, height: 844 },
    screen: { width: 3440, height: 1440 },
    deviceScaleFactor: 2, // M4 Pro typically has 2x retina display
    locale: 'en-US',
    timezoneId: 'America/Los_Angeles',
    permissions: [],
    extraHTTPHeaders: {
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br, zstd',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    }
  });
  
  const page = await context.newPage();
  
  // Override navigator properties to match exactly
  await page.addInitScript(() => {
    // Remove webdriver
    Object.defineProperty(navigator, 'webdriver', {
      get: () => false
    });
    
    // Set platform
    Object.defineProperty(navigator, 'platform', {
      get: () => 'MacIntel'
    });
    
    // Mock plugins to match count
    Object.defineProperty(navigator, 'plugins', {
      get: () => {
        return Object.create(PluginArray.prototype, {
          length: { value: 5 },
          0: { value: { name: 'PDF Viewer', filename: 'internal-pdf-viewer' } },
          1: { value: { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer' } },
          2: { value: { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai' } },
          3: { value: { name: 'Native Client', filename: 'internal-nacl-plugin' } },
          4: { value: { name: 'Chromium PDF Plugin', filename: 'internal-pdf-viewer' } }
        });
      }
    });
    
    // Set languages
    Object.defineProperty(navigator, 'languages', {
      get: () => ['en-US', 'en']
    });
    
    // Mock chrome object
    window.chrome = {
      app: {
        isInstalled: false,
        InstallState: {
          DISABLED: 'disabled',
          INSTALLED: 'installed',
          NOT_INSTALLED: 'not_installed'
        },
        RunningState: {
          CANNOT_RUN: 'cannot_run',
          READY_TO_RUN: 'ready_to_run',
          RUNNING: 'running'
        }
      },
      runtime: {
        connect: () => {},
        sendMessage: () => {}
      },
      loadTimes: function() {
        return {
          commitLoadTime: Date.now() / 1000,
          connectionInfo: 'http/1.1',
          finishDocumentLoadTime: Date.now() / 1000,
          finishLoadTime: Date.now() / 1000,
          firstPaintAfterLoadTime: 0,
          firstPaintTime: Date.now() / 1000,
          navigationType: 'Other',
          npnNegotiatedProtocol: 'unknown',
          requestTime: Date.now() / 1000,
          startLoadTime: Date.now() / 1000,
          wasAlternateProtocolAvailable: false,
          wasFetchedViaSpdy: false,
          wasNpnNegotiated: false
        };
      },
      csi: function() {
        return {
          onloadT: Date.now(),
          pageT: Date.now() - performance.timeOrigin,
          startE: performance.timeOrigin,
          tran: 15
        };
      }
    };
    
    // Override WebGL to match M4 Pro
    const getParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
      // UNMASKED_VENDOR_WEBGL
      if (parameter === 37445) {
        return 'Google Inc. (Apple)';
      }
      // UNMASKED_RENDERER_WEBGL
      if (parameter === 37446) {
        return 'ANGLE (Apple, ANGLE Metal Renderer: Apple M4 Pro, Unspecified Version)';
      }
      return getParameter.apply(this, arguments);
    };
    
    // Also for WebGL2
    if (typeof WebGL2RenderingContext !== 'undefined') {
      const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
      WebGL2RenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) {
          return 'Google Inc. (Apple)';
        }
        if (parameter === 37446) {
          return 'ANGLE (Apple, ANGLE Metal Renderer: Apple M4 Pro, Unspecified Version)';
        }
        return getParameter2.apply(this, arguments);
      };
    }
  });
  
  try {
    console.log('Navigating to Vimeo...');
    await page.goto('https://player.vimeo.com/video/887967850?autoplay=1#t=10s', {
      waitUntil: 'networkidle'
    });
    
    // Wait longer for video to load
    console.log('Waiting for video player to load...');
    await page.waitForTimeout(10000); // 10 seconds
    
    // Take screenshot
    await page.screenshot({ path: 'test4-result.png', fullPage: false });
    console.log('Screenshot saved as test4-result.png');
    
    // Check page state
    const title = await page.title();
    console.log('Page title:', title);
    
    // Check for video player
    const hasVideo = await page.evaluate(() => {
      return document.querySelector('video') !== null;
    });
    console.log('Video element found:', hasVideo);
    
    // Check if blocked
    const content = await page.content();
    if (content.includes('security') || content.includes('blocked')) {
      console.log('❌ Test 4 FAILED - Security verification detected');
    } else if (hasVideo) {
      console.log('✅ Test 4 PASSED - Video player loaded successfully!');
    } else {
      console.log('⚠️ Test 4 PARTIAL - No security block but video not loaded');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    console.log('\nKeeping browser open for inspection. Press Ctrl+C to exit.');
    await new Promise(() => {}); // Keep browser open
  }
})();