// Test 6: Solutions found from research for Vimeo access
const { chromium } = require('playwright');

(async () => {
  console.log('Test 6: Vimeo-specific solutions\n');
  
  const browser = await chromium.launch({
    headless: false,
    args: [
      '--disable-blink-features=AutomationControlled',
      '--disable-web-security',
      '--disable-features=IsolateOrigins,site-per-process',
      // Additional args that might help with video playback
      '--autoplay-policy=no-user-gesture-required',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding'
    ]
  });
  
  const context = await browser.newContext({
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    viewport: { width: 1920, height: 1080 },
    // Important: Allow permissions for video playback
    permissions: ['camera', 'microphone', 'geolocation'],
    // Bypass CSP for video loading
    bypassCSP: true,
    // Accept all downloads (in case video tries to download)
    acceptDownloads: true,
    // No HTTP credentials needed
    // Important: Don't block any resources
    offline: false,
    // Color scheme
    colorScheme: 'light',
    // Extra headers that might help
    extraHTTPHeaders: {
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    }
  });
  
  const page = await context.newPage();
  
  // Comprehensive navigator override
  await page.addInitScript(() => {
    // Remove automation indicators
    Object.defineProperty(navigator, 'webdriver', { get: () => false });
    
    // Mock media devices for video
    if (!navigator.mediaDevices) {
      navigator.mediaDevices = {};
    }
    
    // Override getUserMedia to always succeed
    navigator.mediaDevices.getUserMedia = async () => {
      return {
        getTracks: () => [],
        getVideoTracks: () => [],
        getAudioTracks: () => []
      };
    };
    
    // Ensure video can autoplay
    Object.defineProperty(HTMLMediaElement.prototype, 'canPlayType', {
      value: function() { return 'probably'; }
    });
    
    // Override permissions
    const originalQuery = window.navigator.permissions?.query;
    if (window.navigator.permissions) {
      window.navigator.permissions.query = (parameters) => {
        if (parameters.name === 'camera' || parameters.name === 'microphone') {
          return Promise.resolve({ state: 'granted' });
        }
        return originalQuery ? originalQuery(parameters) : Promise.reject();
      };
    }
  });
  
  // Solution 1: Try direct embed URL
  console.log('Solution 1: Trying direct embed URL...');
  const embedUrl = 'https://player.vimeo.com/video/887967850?autoplay=1&muted=1&background=1';
  
  await page.goto(embedUrl, {
    waitUntil: 'domcontentloaded',
    timeout: 30000
  });
  
  // Wait for Vimeo player to initialize
  console.log('Waiting for Vimeo player...');
  try {
    await page.waitForFunction(() => {
      return window.Vimeo && window.Vimeo.Player;
    }, { timeout: 10000 });
    console.log('Vimeo Player API loaded!');
  } catch (e) {
    console.log('Vimeo Player API not found');
  }
  
  // Wait for video element
  await page.waitForTimeout(5000);
  
  // Solution 2: Try to interact with the player
  console.log('\nSolution 2: Attempting player interaction...');
  
  // Check if we need to click play
  const playButton = await page.$('.play-icon, .vp-promo-wrapper, [aria-label="Play"], button[data-action="play"]');
  if (playButton) {
    console.log('Found play button, clicking...');
    await playButton.click();
    await page.waitForTimeout(3000);
  }
  
  // Check video state
  const videoState = await page.evaluate(() => {
    const video = document.querySelector('video');
    const iframe = document.querySelector('iframe');
    
    return {
      hasVideo: !!video,
      hasIframe: !!iframe,
      videoSrc: video?.src || 'none',
      videoReady: video?.readyState || -1,
      videoPaused: video?.paused,
      videoError: video?.error?.message || 'none',
      pageTitle: document.title,
      bodyText: document.body.innerText.substring(0, 200)
    };
  });
  
  console.log('Video state:', JSON.stringify(videoState, null, 2));
  
  // Solution 3: If it's a private/password-protected video
  if (videoState.bodyText.includes('Private') || videoState.bodyText.includes('Password')) {
    console.log('\nSolution 3: Video appears to be private or password-protected');
    console.log('This video requires authentication or a password to access');
  }
  
  // Solution 4: Try iframe approach
  if (!videoState.hasVideo && !videoState.hasIframe) {
    console.log('\nSolution 4: Creating our own iframe embed...');
    await page.evaluate((url) => {
      document.body.innerHTML = `
        <iframe 
          src="${url}" 
          width="100%" 
          height="100%" 
          frameborder="0" 
          allow="autoplay; fullscreen; picture-in-picture" 
          allowfullscreen
          style="position:absolute;top:0;left:0;width:100%;height:100%;">
        </iframe>
      `;
    }, embedUrl);
    await page.waitForTimeout(5000);
  }
  
  // Take final screenshot
  await page.screenshot({ path: 'test6-result.png', fullPage: true });
  console.log('\nScreenshot saved as test6-result.png');
  
  // Log any console errors from the page
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.log('Page error:', msg.text());
    }
  });
  
  console.log('\nKEY FINDINGS from research:');
  console.log('1. Vimeo videos can be private, password-protected, or domain-restricted');
  console.log('2. The video ID 887967850 might require specific authentication');
  console.log('3. Some Vimeo videos only work when embedded on specific domains');
  console.log('4. Your working browser likely has cookies/session that grants access');
  console.log('\nBrowser staying open for inspection. Press Ctrl+C to exit.');
  
  await new Promise(() => {});
})();