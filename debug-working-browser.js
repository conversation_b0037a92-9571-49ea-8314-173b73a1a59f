// Run this in the console of your WORKING browser to capture its state
// This will help us understand what makes it authorized

console.log('=== DEBUGGING WORKING BROWSER ===\n');

// 1. Check cookies
console.log('1. COOKIES:');
console.log('All cookies:', document.cookie);
console.log('Vimeo cookies specifically:');
if (window.cookieStore) {
  cookieStore.getAll().then(cookies => {
    const vimeoCookies = cookies.filter(c => c.domain.includes('vimeo'));
    console.log('Vimeo cookies via cookieStore:', vimeoCookies);
  });
}

// 2. Check localStorage and sessionStorage
console.log('\n2. STORAGE:');
console.log('LocalStorage keys:', Object.keys(localStorage));
console.log('SessionStorage keys:', Object.keys(sessionStorage));
console.log('LocalStorage (Vimeo-related):');
Object.keys(localStorage).forEach(key => {
  if (key.toLowerCase().includes('vimeo') || key.includes('player')) {
    console.log(`  ${key}:`, localStorage.getItem(key));
  }
});

// 3. Check referrer and navigation
console.log('\n3. NAVIGATION:');
console.log('Current URL:', window.location.href);
console.log('Referrer:', document.referrer);
console.log('Navigation entries:', performance.getEntriesByType('navigation'));

// 4. Check iframe information if in iframe
console.log('\n4. FRAME INFO:');
console.log('Is in iframe:', window.self !== window.top);
console.log('Frame ancestors:', (() => {
  let ancestors = [];
  let currentWindow = window;
  while (currentWindow !== currentWindow.parent) {
    ancestors.push(currentWindow.parent.location.href);
    currentWindow = currentWindow.parent;
  }
  return ancestors;
})());

// 5. Check network requests
console.log('\n5. RECENT NETWORK ACTIVITY:');
const resources = performance.getEntriesByType('resource');
const vimeoResources = resources.filter(r => r.name.includes('vimeo'));
console.log('Vimeo resource loads:', vimeoResources.slice(-10)); // Last 10

// 6. Check for auth tokens in page
console.log('\n6. AUTH TOKENS IN PAGE:');
const scripts = Array.from(document.scripts);
scripts.forEach((script, i) => {
  const content = script.innerHTML;
  if (content.includes('token') || content.includes('auth') || content.includes('jwt')) {
    console.log(`Script ${i} contains auth-related content (truncated):`, content.substring(0, 200) + '...');
  }
});

// 7. Check video element
console.log('\n7. VIDEO ELEMENT:');
const video = document.querySelector('video');
if (video) {
  console.log('Video found!');
  console.log('Video src:', video.src);
  console.log('Video currentSrc:', video.currentSrc);
  console.log('Video readyState:', video.readyState);
  console.log('Video error:', video.error);
  console.log('Can play:', video.canPlayType('video/mp4'));
} else {
  console.log('No video element found');
}

// 8. Check for Vimeo player object
console.log('\n8. VIMEO PLAYER:');
if (window.Vimeo) {
  console.log('Vimeo object exists');
  console.log('Vimeo.Player:', typeof window.Vimeo.Player);
}

// 9. Check page timing
console.log('\n9. PAGE LOAD TIMING:');
const timing = performance.timing;
console.log('Time since navigation start:', Date.now() - timing.navigationStart, 'ms');
console.log('Page load time:', timing.loadEventEnd - timing.navigationStart, 'ms');

// 10. Security context
console.log('\n10. SECURITY CONTEXT:');
console.log('Protocol:', window.location.protocol);
console.log('Is secure context:', window.isSecureContext);
console.log('Origin:', window.origin);

console.log('\n=== END DEBUG ===');
console.log('\nCopy all this output to help debug the issue!');