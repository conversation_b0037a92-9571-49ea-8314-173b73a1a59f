// Script to dump all browser configuration and properties
const { chromium } = require('playwright');

(async () => {
  console.log('=== PLAYWRIGHT BROWSER CONFIGURATION DUMP ===\n');
  
  const browser = await chromium.launch({
    headless: false,
    args: ['--disable-blink-features=AutomationControlled']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Navigate to a test page
  await page.goto('https://httpbin.org/headers');
  
  // 1. Browser Information
  console.log('1. BROWSER INFO:');
  console.log('Browser Type:', browser.browserType().name());
  console.log('Browser Version:', browser.version());
  console.log('Is Connected:', browser.isConnected());
  
  // 2. Context Configuration
  console.log('\n2. CONTEXT CONFIGURATION:');
  const contextOptions = await page.evaluate(() => {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      vendor: navigator.vendor,
      language: navigator.language,
      languages: navigator.languages,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      hardwareConcurrency: navigator.hardwareConcurrency,
      deviceMemory: navigator.deviceMemory,
      maxTouchPoints: navigator.maxTouchPoints
    };
  });
  console.log(JSON.stringify(contextOptions, null, 2));
  
  // 3. Viewport and Screen Info
  console.log('\n3. VIEWPORT & SCREEN:');
  const viewportInfo = await page.evaluate(() => {
    return {
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: screen.width,
        height: screen.height,
        availWidth: screen.availWidth,
        availHeight: screen.availHeight,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth
      },
      devicePixelRatio: window.devicePixelRatio
    };
  });
  console.log(JSON.stringify(viewportInfo, null, 2));
  
  // 4. WebDriver and Automation Detection
  console.log('\n4. AUTOMATION DETECTION:');
  const automationInfo = await page.evaluate(() => {
    return {
      webdriver: navigator.webdriver,
      hasChrome: !!window.chrome,
      hasChromRuntime: !!(window.chrome && window.chrome.runtime),
      plugins: navigator.plugins.length,
      permissions: typeof navigator.permissions !== 'undefined',
      webgl: (() => {
        try {
          const canvas = document.createElement('canvas');
          const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
          if (!gl) return 'not supported';
          const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
          return {
            vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
            renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
          };
        } catch (e) {
          return 'error: ' + e.message;
        }
      })()
    };
  });
  console.log(JSON.stringify(automationInfo, null, 2));
  
  // 5. HTTP Headers
  console.log('\n5. HTTP HEADERS (from httpbin):');
  const headers = await page.evaluate(() => {
    const pre = document.querySelector('pre');
    return pre ? JSON.parse(pre.textContent) : null;
  });
  console.log(JSON.stringify(headers, null, 2));
  
  // 6. Browser Launch Arguments (if accessible)
  console.log('\n6. BROWSER PROCESS INFO:');
  try {
    const browserProcess = browser.process();
    if (browserProcess) {
      console.log('Process PID:', browserProcess.pid);
      console.log('Process SpawnArgs:', browserProcess.spawnargs);
    }
  } catch (e) {
    console.log('Cannot access browser process info:', e.message);
  }
  
  // 7. CDP Session Info
  console.log('\n7. CDP SESSION:');
  try {
    const client = await page.context().newCDPSession(page);
    const version = await client.send('Browser.getVersion');
    console.log('Browser Version Info:', JSON.stringify(version, null, 2));
    await client.detach();
  } catch (e) {
    console.log('Cannot access CDP info:', e.message);
  }
  
  // 8. Page Metrics
  console.log('\n8. PAGE METRICS:');
  const metrics = await page.metrics();
  console.log(JSON.stringify(metrics, null, 2));
  
  // Save full page evaluation
  await page.evaluate(() => {
    console.log('\n=== BROWSER CONSOLE DUMP ===');
    console.log('All window properties:', Object.keys(window));
    console.log('Navigator properties:', Object.keys(navigator));
  });
  
  console.log('\n=== END OF DUMP ===');
  
  // Keep browser open for manual inspection
  console.log('\nBrowser will stay open. Press Ctrl+C to exit.');
  
  // Prevent script from exiting
  await new Promise(() => {});
})();