# Playwright Evasion Test Scripts

Three test scripts to evaluate different evasion techniques for bypassing Cloudflare/security verification.

## Installation

```bash
cd test-scripts
npm install
```

For Test 3 (optional):
```bash
npm install playwright-extra puppeteer-extra-plugin-stealth
```

## Running Tests

Individual tests:
```bash
npm run test1  # Basic evasion
npm run test2  # Comprehensive evasion
npm run test3  # Playwright-extra with stealth plugin
```

Run all tests:
```bash
npm run test-all
```

## Test Descriptions

### Test 1: Basic Evasion
- Minimal setup with just `--disable-blink-features=AutomationControlled`
- Custom user agent
- Non-headless mode

### Test 2: Comprehensive Evasion  
- Multiple browser arguments
- Full context configuration (viewport, locale, timezone)
- JavaScript injection to remove automation indicators
- Mock chrome object and navigator properties

### Test 3: Playwright-Extra with Stealth
- Uses playwright-extra with puppeteer-extra-plugin-stealth
- WebGL fingerprint spoofing
- Geolocation settings
- All stealth plugin evasions

## Expected Results

Each test will:
1. Navigate to the Vimeo video URL
2. Wait 5 seconds
3. Take a screenshot (test1-result.png, test2-result.png, test3-result.png)
4. Check if security verification was triggered
5. Report PASSED or FAILED status