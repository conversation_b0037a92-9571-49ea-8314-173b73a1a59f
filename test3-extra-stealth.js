// Test 3: Playwright-extra with stealth plugin
const { chromium } = require('playwright-extra');

// Check if stealth plugin is available
let stealth;
try {
  stealth = require('puppeteer-extra-plugin-stealth');
} catch (e) {
  console.log('Note: puppeteer-extra-plugin-stealth not installed');
  console.log('Install with: npm install playwright-extra puppeteer-extra-plugin-stealth');
}

(async () => {
  console.log('Test 3: Playwright-extra with stealth plugin');
  
  if (stealth) {
    chromium.use(stealth());
  }
  
  const browser = await chromium.launch({
    headless: false,
    args: [
      '--disable-blink-features=AutomationControlled',
      '--disable-web-security',
      '--window-size=1920,1080'
    ]
  });
  
  const context = await browser.newContext({
    userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport: { width: 1920, height: 1080 },
    locale: 'en-US',
    timezoneId: 'America/New_York',
    geolocation: { longitude: -74.0060, latitude: 40.7128 },
    permissions: ['geolocation']
  });
  
  const page = await context.newPage();
  
  // Additional evasion for WebGL
  await page.addInitScript(() => {
    const getParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
      // UNMASKED_VENDOR_WEBGL
      if (parameter === 37445) {
        return 'Intel Inc.';
      }
      // UNMASKED_RENDERER_WEBGL
      if (parameter === 37446) {
        return 'Intel Iris OpenGL Engine';
      }
      return getParameter(parameter);
    };
  });
  
  try {
    await page.goto('https://player.vimeo.com/video/887967850?autoplay=1#t=10s');
    await page.waitForTimeout(5000);
    
    // Take screenshot
    await page.screenshot({ path: 'test3-result.png' });
    console.log('Screenshot saved as test3-result.png');
    
    // Check if we got blocked
    const title = await page.title();
    const content = await page.content();
    
    if (content.includes('security') || content.includes('blocked')) {
      console.log('❌ Test 3 FAILED - Security verification detected');
    } else {
      console.log('✅ Test 3 PASSED - No security block detected');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await browser.close();
  }
})();