# Playwright MCP Server

A programmatic implementation of Playwright MCP (Model Context Protocol) server with SSE (Server-Sent Events) transport.

## Requirements

- Node.js 18 or newer
- pnpm (or npm/yarn)

## Installation

```bash
pnpm install
```

## Usage

### Starting the Server

```bash
# Standard mode
pnpm start

# Development mode with auto-reload
pnpm dev
```

The server will start on port 3000 by default (or use `PORT` environment variable).

### Endpoints

- `GET /` - Server info page
- `GET /health` - Health check endpoint
- `POST /messages` - SSE endpoint for MCP communication

### Example Usage

The server creates a headless Playwright browser instance and establishes an MCP connection using SSE transport. When a client connects to the `/messages` endpoint, it can communicate with the Playwright browser through the MCP protocol.

### Configuration

The server runs with these default browser options:
- Headless mode
- No-sandbox (for containerized environments)
- Disable-setuid-sandbox

You can modify these in `server.js` as needed.

### Testing

Use the provided `example-client.js` to test the connection:

```bash
node example-client.js
```

## Architecture

The server uses:
- `@playwright/mcp` - <PERSON>wright's MCP implementation
- `@modelcontextprotocol/sdk` - MCP SDK for SSE transport
- Native Node.js HTTP server

## Error Handling

The server includes error handling for:
- Connection failures
- Invalid endpoints (404)
- Server errors (500)