import { createConnection } from '@playwright/mcp';
import { BrowserServerBackend } from '@playwright/mcp/lib/browserServerBackend.js';

async function inspectAPI() {
  console.log('Inspecting Playwright MCP API...\n');
  
  try {
    // Create a connection
    const connection = await createConnection({
      browser: {
        launchOptions: {
          headless: false
        }
      }
    });
    
    console.log('Connection type:', connection.constructor.name);
    console.log('Connection properties:', Object.keys(connection));
    
    // Check for backend
    if (connection._backend) {
      console.log('\nBackend found!');
      console.log('Backend type:', connection._backend.constructor.name);
      console.log('Backend methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(connection._backend)));
      
      // Try to navigate
      console.log('\nTrying to navigate to actura.dk...');
      const result = await connection._backend.callTool({
        name: 'browser_navigate', 
        arguments: { url: 'https://actura.dk' }
      });
      
      console.log('Navigation result:', result);
      
      // Keep it open
      console.log('\nPress Ctrl+C to exit');
      
    } else {
      // Look for other ways to access the browser
      console.log('\nLooking for other properties...');
      for (const prop of Object.keys(connection)) {
        const value = connection[prop];
        if (value && typeof value === 'object') {
          console.log(`${prop}:`, value.constructor?.name || typeof value);
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

inspectAPI().catch(console.error);