import { PlaywrightMCPWrapper } from './playwright-wrapper.js';

async function navigateToActura() {
  const browser = new PlaywrightMCPWrapper();
  
  try {
    // Connect with headed browser (default)
    await browser.connect();
    
    // Navigate to actura.dk
    await browser.navigate('https://actura.dk');
    
    // Wait 5 seconds to see the page load
    console.log('\nWaiting 5 seconds to see the page...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Take a screenshot
    await browser.screenshot('actura-loaded.png');
    
    // Get and display page info
    const snapshot = await browser.snapshot();
    console.log('\nPage snapshot info:');
    if (snapshot.content && snapshot.content.length > 0) {
      console.log('- Title:', snapshot.content[0].text || 'No title');
      console.log('- Total elements:', snapshot.content.length);
    }
    
    // Wait another 10 seconds before closing
    console.log('\nKeeping browser open for 10 more seconds...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await browser.close();
  }
}

// Run it
console.log('Navigating to https://actura.dk with Playwright MCP...\n');
navigateToActura().catch(console.error);