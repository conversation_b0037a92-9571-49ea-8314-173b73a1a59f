import fetch from 'node-fetch';

const SERVER_URL = 'http://localhost:3090';

async function testActura() {
  console.log('Testing Playwright MCP Server with actura.dk...\n');
  
  // First, check if server is running
  try {
    const healthResponse = await fetch(`${SERVER_URL}/health`);
    const health = await healthResponse.json();
    console.log('✓ Server health check:', health);
  } catch (error) {
    console.error('✗ Server is not running. Start it with: npm start');
    process.exit(1);
  }
  
  // Create a session and navigate to actura.dk
  console.log('\nSending navigation command to actura.dk...');
  
  try {
    // Since this is an SSE-based MCP server, we need to send proper MCP protocol messages
    // For now, let's create a simple HTTP client that can interact with the server
    
    // Note: The actual MCP protocol over SSE is more complex and requires proper message formatting
    // This is a simplified example to show the concept
    
    const response = await fetch(`${SERVER_URL}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: 'browser_navigate',
          arguments: {
            url: 'https://actura.dk'
          }
        },
        id: 1
      })
    });
    
    if (response.ok) {
      console.log('✓ Navigation command sent successfully');
      
      // Wait a moment for navigation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Take a screenshot
      console.log('\nTaking screenshot...');
      const screenshotResponse = await fetch(`${SERVER_URL}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'tools/call',
          params: {
            name: 'browser_take_screenshot',
            arguments: {
              filename: 'actura-homepage.png'
            }
          },
          id: 2
        })
      });
      
      if (screenshotResponse.ok) {
        console.log('✓ Screenshot saved as actura-homepage.png');
      }
      
      // Get page snapshot
      console.log('\nGetting page snapshot...');
      const snapshotResponse = await fetch(`${SERVER_URL}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'tools/call',
          params: {
            name: 'browser_snapshot',
            arguments: {}
          },
          id: 3
        })
      });
      
      if (snapshotResponse.ok) {
        console.log('✓ Page snapshot retrieved');
      }
      
    } else {
      console.log('✗ Failed to send navigation command:', response.status);
    }
  } catch (error) {
    console.error('✗ Error:', error.message);
  }
  
  console.log('\nTest completed.');
}

// Run the test
testActura().catch(console.error);