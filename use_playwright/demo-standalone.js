#!/usr/bin/env node

// This demonstrates running the Playwright MCP server in standalone mode
// and connecting to it via SSE

console.log('Playwright MCP Server Demo - Navigate to actura.dk');
console.log('================================================\n');

console.log('To test navigation to https://actura.dk:');
console.log('\n1. First, start the MCP server in standalone mode:');
console.log('   npx @playwright/mcp@latest --port 3090\n');

console.log('2. Then configure your MCP client (Claude, VS Code, etc.) to connect to:');
console.log('   http://localhost:3090/sse\n');

console.log('3. Once connected, you can use these commands:');
console.log('   - Navigate: "Navigate to https://actura.dk"');
console.log('   - Screenshot: "Take a screenshot of the current page"');
console.log('   - Interact: "Click on the contact button"');
console.log('   - Extract: "What services does Actura offer?"\n');

console.log('Example VS Code/Cursor configuration:');
console.log(JSON.stringify({
  "mcpServers": {
    "playwright": {
      "url": "http://localhost:3090/sse"
    }
  }
}, null, 2));

console.log('\nNote: The Playwright MCP server is designed to be used with AI assistants');
console.log('like Claude or GitHub Copilot, not as a traditional API.');