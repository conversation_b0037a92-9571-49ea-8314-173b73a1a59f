#!/usr/bin/env node

import { spawn } from 'child_process';
import readline from 'readline';

console.log('Starting Playwright MCP in headed mode...\n');

// Start the server with stdio pipes
const server = spawn('npx', ['@playwright/mcp@latest', '--no-sandbox'], {
  stdio: ['pipe', 'pipe', 'pipe']
});

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Handle server output
server.stdout.on('data', (data) => {
  const output = data.toString();
  if (output.includes('error')) {
    console.error('Server:', output);
  }
});

server.stderr.on('data', (data) => {
  console.error('Server error:', data.toString());
});

// Wait a bit for server to start
setTimeout(() => {
  console.log('Browser should be open now.\n');
  console.log('Available commands:');
  console.log('  nav <url>     - Navigate to URL');
  console.log('  shot [name]   - Take screenshot');
  console.log('  quit          - Exit\n');
  
  // Simple command loop
  const askCommand = () => {
    rl.question('> ', (answer) => {
      const [cmd, ...args] = answer.trim().split(' ');
      
      switch(cmd) {
        case 'nav':
          if (args[0]) {
            console.log(`Navigating to ${args[0]}...`);
            // In a real implementation, we'd send MCP commands here
            // For now, just log it
            console.log('(Navigation would happen here via MCP protocol)');
          } else {
            console.log('Usage: nav <url>');
          }
          break;
          
        case 'shot':
          const filename = args[0] || `screenshot-${Date.now()}.png`;
          console.log(`Taking screenshot: ${filename}`);
          console.log('(Screenshot would be taken here via MCP protocol)');
          break;
          
        case 'quit':
          console.log('Shutting down...');
          server.kill();
          process.exit(0);
          break;
          
        default:
          if (cmd) console.log('Unknown command:', cmd);
      }
      
      askCommand();
    });
  };
  
  // Direct navigation to actura.dk
  console.log('Navigating to https://actura.dk...\n');
  console.log('(The browser is open but we need to implement MCP protocol communication)');
  console.log('For now, you can manually navigate in the opened browser.\n');
  
  askCommand();
}, 2000);

// Handle exit
process.on('SIGINT', () => {
  console.log('\nShutting down...');
  rl.close();
  server.kill();
  process.exit(0);
});