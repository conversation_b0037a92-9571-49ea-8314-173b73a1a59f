import { spawn } from 'child_process';

// Simply run the Playwright MCP server directly in headed mode
const server = spawn('npx', ['@playwright/mcp@latest'], {
  stdio: 'inherit',
  env: { ...process.env }
});

console.log('Playwright MCP server started in headed mode.');
console.log('The browser should open automatically.');
console.log('\nTo use it:');
console.log('1. Connect your MCP client to this server');
console.log('2. Or use the browser manually');
console.log('\nPress Ctrl+C to stop the server.');

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down...');
  server.kill();
  process.exit(0);
});