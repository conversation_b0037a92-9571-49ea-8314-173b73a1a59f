import http from 'http';
import { createConnection } from '@playwright/mcp';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';

const PORT = process.env.PORT || 3090;

http.createServer(async (req, res) => {
  console.log(`${req.method} ${req.url}`);
  
  if (req.url === '/messages' && req.method === 'POST') {
    try {
      // Creates a headless Playwright MCP server with SSE transport
      const server = await createConnection({ 
        browser: { 
          launchOptions: { 
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
          } 
        } 
      });
      
      const transport = new SSEServerTransport('/messages', res);
      await server.connect(transport);
      
    } catch (error) {
      console.error('Error creating connection:', error);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: error.message }));
    }
  } else if (req.url === '/health' && req.method === 'GET') {
    // Health check endpoint
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() }));
  } else if (req.url === '/' && req.method === 'GET') {
    // Basic info endpoint
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <html>
        <head><title>Playwright MCP Server</title></head>
        <body>
          <h1>Playwright MCP Server</h1>
          <p>Server is running on port ${PORT}</p>
          <p>Endpoints:</p>
          <ul>
            <li>POST /messages - SSE endpoint for MCP communication</li>
            <li>GET /health - Health check endpoint</li>
          </ul>
        </body>
      </html>
    `);
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
}).listen(PORT, () => {
  console.log(`Playwright MCP server running on http://localhost:${PORT}`);
  console.log('Ready to accept connections...');
});