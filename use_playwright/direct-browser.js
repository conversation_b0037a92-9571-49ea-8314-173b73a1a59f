import { createConnection } from '@playwright/mcp';

async function navigateToActura() {
  console.log('Starting Playwright MCP browser...\n');
  
  try {
    // Create a browser connection with headed mode
    const connection = await createConnection({
      browser: {
        launchOptions: {
          headless: false,  // Run in headed mode so you can see it
          args: ['--start-maximized']
        }
      }
    });
    
    console.log('✓ Browser started successfully');
    
    // The connection object is actually an MCP server
    // We need to interact with it through the MCP protocol
    // But we can create a simple wrapper to use it directly
    
    // Get the backend from the connection
    const backend = connection._backend;
    
    if (backend) {
      console.log('✓ Backend available');
      
      // Call the browser_navigate tool directly
      const navigateResult = await backend.callTool({
        name: 'browser_navigate',
        arguments: { url: 'https://actura.dk' }
      });
      
      console.log('✓ Navigated to https://actura.dk');
      console.log('Result:', navigateResult);
      
      // Wait a bit to see the page
      console.log('\nWaiting 5 seconds to view the page...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Take a screenshot
      const screenshotResult = await backend.callTool({
        name: 'browser_take_screenshot',
        arguments: { filename: 'actura-screenshot.png' }
      });
      
      console.log('✓ Screenshot saved');
      
      // Get page snapshot (accessibility tree)
      const snapshotResult = await backend.callTool({
        name: 'browser_snapshot',
        arguments: {}
      });
      
      console.log('✓ Page snapshot retrieved');
      console.log('Page title:', snapshotResult.content?.[0]?.text || 'Unknown');
      
      // Keep browser open for manual inspection
      console.log('\nBrowser is still open. Press Ctrl+C to exit.');
      
    } else {
      console.log('✗ Could not access backend directly');
    }
    
  } catch (error) {
    console.error('✗ Error:', error);
  }
}

// Run it
navigateToActura().catch(console.error);