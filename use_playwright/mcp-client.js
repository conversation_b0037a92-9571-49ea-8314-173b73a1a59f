import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

async function runMCPClient() {
  console.log('Starting Playwright MCP client...\n');
  
  // Spawn the server process
  const serverProcess = spawn('node', ['server.js'], {
    stdio: ['pipe', 'pipe', 'pipe'],
  });
  
  // Create MCP client with stdio transport
  const transport = new StdioClientTransport({
    command: 'node',
    args: ['server.js'],
  });
  
  const client = new Client({
    name: 'playwright-test-client',
    version: '1.0.0',
  }, {
    capabilities: {}
  });
  
  try {
    // Connect to the server
    await client.connect(transport);
    console.log('✓ Connected to Playwright MCP server');
    
    // List available tools
    const tools = await client.listTools();
    console.log('\nAvailable tools:', tools.tools.length);
    tools.tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    
    // Example: Navigate to a URL
    console.log('\nNavigating to example.com...');
    const result = await client.callTool('browser_navigate', {
      url: 'https://example.com'
    });
    console.log('✓ Navigation result:', result);
    
    // Take a snapshot
    console.log('\nTaking page snapshot...');
    const snapshot = await client.callTool('browser_snapshot', {});
    console.log('✓ Snapshot taken successfully');
    
    // Close the browser
    console.log('\nClosing browser...');
    await client.callTool('browser_close', {});
    console.log('✓ Browser closed');
    
  } catch (error) {
    console.error('✗ Error:', error.message);
  } finally {
    // Clean up
    await transport.close();
    serverProcess.kill();
    console.log('\nClient disconnected.');
  }
}

// Run the client
runMCPClient().catch(console.error);