import { createConnection } from '@playwright/mcp';

async function runBrowser() {
  console.log('Starting Playwright MCP in headed mode...\n');
  
  try {
    // Create connection with headed browser
    const server = await createConnection({
      browser: {
        launchOptions: {
          headless: false,
          args: ['--start-maximized']
        }
      }
    });
    
    console.log('✓ Browser launched');
    console.log('Server type:', server.constructor.name);
    
    // The server has request handlers set up for MCP tools
    // Let's simulate tool calls
    
    // Create a mock request for navigation
    const navigateRequest = {
      method: 'tools/call',
      params: {
        name: 'browser_navigate',
        arguments: { url: 'https://actura.dk' }
      }
    };
    
    // Try to find and call the handler
    if (server._requestHandlers) {
      console.log('Found request handlers');
      const toolHandler = server._requestHandlers.get('tools/call');
      if (toolHandler) {
        console.log('Calling browser_navigate...');
        const result = await toolHandler(navigateRequest);
        console.log('Result:', result);
      }
    }
    
    // Keep browser open
    console.log('\nBrowser is open. Press Ctrl+C to exit.');
    
    // Prevent process from exiting
    process.stdin.resume();
    
  } catch (error) {
    console.error('Error:', error);
  }
}

runBrowser().catch(console.error);