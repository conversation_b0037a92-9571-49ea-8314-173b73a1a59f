import { EventSource } from 'eventsource';

const SERVER_URL = 'http://localhost:3090';

// Simple SSE client example
async function testConnection() {
  console.log('Testing Playwright MCP Server connection...\n');
  
  // First, check if server is running
  try {
    const healthResponse = await fetch(`${SERVER_URL}/health`);
    const health = await healthResponse.json();
    console.log('✓ Server health check:', health);
  } catch (error) {
    console.error('✗ Server is not running. Start it with: pnpm start');
    process.exit(1);
  }
  
  // Connect to SSE endpoint
  console.log('\nConnecting to SSE endpoint...');
  const eventSource = new EventSource(`${SERVER_URL}/messages`);
  
  eventSource.onopen = () => {
    console.log('✓ SSE connection established');
  };
  
  eventSource.onmessage = (event) => {
    console.log('Message received:', event.data);
  };
  
  eventSource.onerror = (error) => {
    console.error('✗ SSE connection error:', error);
    eventSource.close();
  };
  
  // Send a test message after connection
  setTimeout(async () => {
    console.log('\nSending test message...');
    try {
      const response = await fetch(`${SERVER_URL}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'test',
          message: 'Hello from client'
        })
      });
      
      if (response.ok) {
        console.log('✓ Message sent successfully');
      } else {
        console.log('✗ Failed to send message:', response.status);
      }
    } catch (error) {
      console.error('✗ Error sending message:', error.message);
    }
  }, 1000);
  
  // Keep the client running
  console.log('\nClient is running. Press Ctrl+C to exit.');
}

// Run the test
testConnection().catch(console.error);