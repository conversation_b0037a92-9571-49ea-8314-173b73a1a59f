import { spawn } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class PlaywrightMCPWrapper {
  constructor() {
    this.client = null;
    this.transport = null;
    this.serverProcess = null;
  }

  async connect(options = {}) {
    console.log('Starting Playwright MCP server...');
    
    // Prepare arguments for the MCP server
    const args = ['@playwright/mcp@latest'];
    
    // It's headed by default, only add --headless if explicitly requested
    if (options.headless) {
      args.push('--headless');
    }
    
    if (options.port) {
      args.push('--port', options.port.toString());
    }
    
    // Create transport with command
    this.transport = new StdioClientTransport({
      command: 'npx',
      args: args,
      env: { ...process.env }
    });
    
    this.client = new Client({
      name: 'playwright-wrapper',
      version: '1.0.0'
    }, {
      capabilities: {}
    });
    
    // Connect
    await this.client.connect(this.transport);
    console.log('✓ Connected to Playwright MCP server');
    
    return this;
  }
  
  async navigate(url) {
    console.log(`Navigating to ${url}...`);
    const result = await this.client.callTool('browser_navigate', { url });
    console.log('✓ Navigation complete');
    return result;
  }
  
  async screenshot(filename) {
    console.log(`Taking screenshot: ${filename}...`);
    const result = await this.client.callTool('browser_take_screenshot', { 
      filename: filename || `screenshot-${Date.now()}.png` 
    });
    console.log('✓ Screenshot saved');
    return result;
  }
  
  async snapshot() {
    console.log('Getting page snapshot...');
    const result = await this.client.callTool('browser_snapshot', {});
    console.log('✓ Snapshot retrieved');
    return result;
  }
  
  async click(element, ref) {
    console.log(`Clicking: ${element}...`);
    const result = await this.client.callTool('browser_click', { element, ref });
    console.log('✓ Click performed');
    return result;
  }
  
  async type(element, ref, text) {
    console.log(`Typing in: ${element}...`);
    const result = await this.client.callTool('browser_type', { element, ref, text });
    console.log('✓ Text typed');
    return result;
  }
  
  async listTools() {
    const tools = await this.client.listTools();
    return tools.tools;
  }
  
  async close() {
    console.log('Closing browser...');
    try {
      await this.client.callTool('browser_close', {});
    } catch (e) {
      // Browser might already be closed
    }
    
    if (this.transport) {
      await this.transport.close();
    }
    
    if (this.serverProcess) {
      this.serverProcess.kill();
    }
    
    console.log('✓ Browser closed');
  }
}

// Example usage
async function demo() {
  const browser = new PlaywrightMCPWrapper();
  
  try {
    // Connect with headed browser
    await browser.connect({ headless: false });
    
    // Navigate to actura.dk
    await browser.navigate('https://actura.dk');
    
    // Wait a bit to see the page
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Take a screenshot
    await browser.screenshot('actura-homepage.png');
    
    // Get page snapshot
    const snapshot = await browser.snapshot();
    console.log('\nPage contains:', snapshot.content?.[0]?.text || 'No content');
    
    // Keep browser open for inspection
    console.log('\nBrowser is open. Press Enter to close...');
    await new Promise(resolve => {
      process.stdin.once('data', resolve);
    });
    
  } finally {
    await browser.close();
  }
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  demo().catch(console.error);
}

export { PlaywrightMCPWrapper };