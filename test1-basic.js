// Test 1: Basic Playwright with minimal evasion
const { chromium } = require('playwright');

(async () => {
  console.log('Test 1: Basic Playwright with minimal evasion');
  
  const browser = await chromium.launch({
    headless: false,
    args: ['--disable-blink-features=AutomationControlled']
  });
  
  const context = await browser.newContext({
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36'
  });
  
  const page = await context.newPage();
  
  try {
    await page.goto('https://player.vimeo.com/video/887967850?autoplay=1#t=10s');
    await page.waitForTimeout(5000);
    
    // Take screenshot
    await page.screenshot({ path: 'test1-result.png' });
    console.log('Screenshot saved as test1-result.png');
    
    // Check if we got blocked
    const title = await page.title();
    const content = await page.content();
    
    if (content.includes('security') || content.includes('blocked')) {
      console.log('❌ Test 1 FAILED - Security verification detected');
    } else {
      console.log('✅ Test 1 PASSED - No security block detected');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await browser.close();
  }
})();