// Test 5: Try different approaches that might work
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

(async () => {
  console.log('Test 5: Testing different context approaches\n');
  
  // Approach 1: Use persistent context (might have saved cookies)
  console.log('1. Trying with persistent context...');
  const userDataDir = path.join(__dirname, 'browser-data');
  
  const context = await chromium.launchPersistentContext(userDataDir, {
    headless: false,
    args: [
      '--disable-blink-features=AutomationControlled',
      '--no-sandbox',
      '--disable-setuid-sandbox'
    ],
    viewport: { width: 1705, height: 844 },
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  });
  
  const page = await context.newPage();
  
  // Remove webdriver flag
  await page.addInitScript(() => {
    Object.defineProperty(navigator, 'webdriver', {
      get: () => false
    });
  });
  
  // Approach 2: Try navigating from Vimeo main site first
  console.log('2. Navigating to Vimeo main site first...');
  await page.goto('https://vimeo.com', { waitUntil: 'networkidle' });
  await page.waitForTimeout(3000);
  
  console.log('3. Now navigating to the video...');
  await page.goto('https://player.vimeo.com/video/887967850?autoplay=1#t=10s', {
    waitUntil: 'networkidle',
    referer: 'https://vimeo.com'
  });
  
  console.log('4. Waiting for video to load...');
  await page.waitForTimeout(10000);
  
  // Check video state
  const videoInfo = await page.evaluate(() => {
    const video = document.querySelector('video');
    if (video) {
      return {
        found: true,
        src: video.src,
        readyState: video.readyState,
        paused: video.paused,
        duration: video.duration,
        error: video.error ? video.error.message : null
      };
    }
    return { found: false };
  });
  
  console.log('Video info:', videoInfo);
  
  // Save screenshot
  await page.screenshot({ path: 'test5-result.png' });
  console.log('Screenshot saved as test5-result.png');
  
  // Save cookies for future use
  const cookies = await context.cookies();
  fs.writeFileSync('cookies.json', JSON.stringify(cookies, null, 2));
  console.log('Cookies saved to cookies.json');
  
  // Try approach 3: Direct video URL if found
  if (videoInfo.found && videoInfo.src) {
    console.log('\n5. Found video source, trying direct access...');
    const page2 = await context.newPage();
    await page2.goto(videoInfo.src);
    await page2.waitForTimeout(3000);
    await page2.screenshot({ path: 'test5-direct-video.png' });
    console.log('Direct video screenshot saved');
  }
  
  console.log('\nTest complete. Browser will stay open. Press Ctrl+C to exit.');
  await new Promise(() => {});
})();