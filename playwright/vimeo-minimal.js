const { chromium } = require('playwright');

(async () => {
  // The key is using channel: 'chrome' instead of default Chromium
  const browser = await chromium.launch({ 
    channel: 'chrome',  // This prevents the Vimeo player error!
    headless: false
  });
  
  const context = await browser.newContext({
    viewport: null  // Use full browser window width
  });
  const page = await context.newPage();
  
  await page.goto('https://player.vimeo.com/video/887967850?background=1');
  await page.waitForLoadState('networkidle');
  
  console.log('Page loaded successfully');
  
  // Wait a bit to see the video
  await page.waitForTimeout(5000);
  
  // Check if there's an error
  const errorText = await page.textContent('body');
  if (errorText.includes('Player error')) {
    console.log('ERROR: Vimeo player failed to load');
  } else {
    console.log('SUCCESS: Vimeo player loaded without errors');
  }
  
  await browser.close();
})();