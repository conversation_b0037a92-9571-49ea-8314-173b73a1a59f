# Complete Guide to Undetectable Headless Chrome with <PERSON><PERSON>

## Overview

This guide documents our research into running Chrome in headless mode while avoiding detection. We discovered that detection sophistication varies greatly between sites, from simple user-agent checks to complex fingerprinting.

## Quick Solutions (From Simplest to Most Complex)

### 1. Minimal Fix (Works for Vimeo and similar sites)

```javascript
const { chromium } = require('playwright');

const browser = await chromium.launch({
  channel: 'chrome',
  headless: true
});

const context = await browser.newContext({
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
});
```

**That's it!** Just remove "HeadlessChrome" from the user agent.

### 2. Standard Protection (Recommended for most sites)

```javascript
const browser = await chromium.launch({
  channel: 'chrome',  // Use real Chrome, not Chromium
  headless: true,
  args: ['--disable-blink-features=AutomationControlled']
});

const context = await browser.newContext({
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  viewport: { width: 1920, height: 1080 },
  screen: { width: 1920, height: 1080 }
});

const page = await context.newPage();

// Hide webdriver property
await page.addInitScript(() => {
  Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined
  });
});
```

### 3. Advanced Protection (For sophisticated detection)

```javascript
// First install: npm install playwright-extra playwright-extra-plugin-stealth

const { chromium } = require('playwright-extra');
const stealth = require('playwright-extra-plugin-stealth')();

chromium.use(stealth);

const browser = await chromium.launch({
  headless: true,
  args: ['--no-sandbox']
});
```

## Detection Methods Used by Websites

### 1. User Agent String (Most Common)
- **Check for:** "HeadlessChrome" in user agent
- **Fix:** Replace with normal Chrome user agent
- **Used by:** Vimeo, many media sites

### 2. Navigator.webdriver Property
- **Check for:** `navigator.webdriver === true`
- **Fix:** Use `--disable-blink-features=AutomationControlled` or override with addInitScript
- **Used by:** More sophisticated sites

### 3. Browser Features
- **Check for:** Missing plugins, wrong screen dimensions, missing Chrome object
- **Fix:** Set proper viewport/screen, add fake plugins
- **Used by:** Bot detection services

### 4. Advanced Fingerprinting
- **Check for:** WebGL parameters, canvas fingerprinting, timing patterns
- **Fix:** Use playwright-extra-plugin-stealth
- **Used by:** Advanced anti-bot services

## Our Testing Results

### Vimeo Player Test
- **Detection method:** Only checks for "HeadlessChrome" in user agent
- **Minimal fix works:** ✅ Just changing user agent is enough
- **navigator.webdriver:** Still returns `true` but Vimeo doesn't check it

### Essential Scripts in This Directory

1. **headless-simplest.js** - Minimal headless example with screenshot
   - Shows the absolute minimum needed: just fix the user agent
   - Takes a screenshot to prove it works

2. **vimeo-minimal.js** - Proves `channel: 'chrome'` prevents Vimeo errors
   - Demonstrates the original issue we solved
   - Shows viewport configuration for full-width video

3. **launch-actura.js** - Replicates playwright-mcp browser configuration
   - Uses persistent context like playwright-mcp
   - Includes all the settings we discovered through testing

## Key Findings

### 1. Chrome's New Headless Mode
- Chrome now has a new headless implementation closer to regular Chrome
- Access it with `channel: 'chrome'` or `channel: 'chromium'`
- Old headless shell is still default in plain Chromium

### 2. The "--disable-blink-features=AutomationControlled" Flag
- Disables Chrome's automation indicators
- Prevents `navigator.webdriver = true`
- Most effective single flag for basic evasion

### 3. User Agent is Often Enough
- Many sites only check for "HeadlessChrome" string
- Simple string replacement often works
- Default headless UA: `Mozilla/5.0 ... HeadlessChrome/********* Safari/537.36`
- Fixed UA: `Mozilla/5.0 ... Chrome/********* Safari/537.36`

## Troubleshooting Guide

### Site Still Detects Headless?

1. **Check what they're detecting:**
   ```javascript
   console.log('webdriver:', await page.evaluate(() => navigator.webdriver));
   console.log('user agent:', await page.evaluate(() => navigator.userAgent));
   console.log('plugins:', await page.evaluate(() => navigator.plugins.length));
   ```

2. **Try progressively stronger solutions:**
   - Start with just user agent
   - Add `--disable-blink-features=AutomationControlled`
   - Add viewport and screen dimensions
   - Override navigator properties with addInitScript
   - Use playwright-extra-plugin-stealth

3. **For specific issues:**
   - **Timeouts:** Some sites load differently in headless, add longer waits
   - **Missing features:** Headless may lack GPU features, try `--use-gl=swiftshader`
   - **Captchas:** These often require real interaction, consider headed mode

### Common Pitfalls

1. **Using default Chromium instead of Chrome channel**
   - Default uses old headless shell
   - Always use `channel: 'chrome'` for better compatibility

2. **Not setting viewport**
   - Headless defaults to 800x600
   - Set realistic viewport: `{ width: 1920, height: 1080 }`

3. **Forgetting about persistent contexts**
   - playwright-mcp uses persistent contexts by default
   - These can conflict if profile directory is in use
   - Use different profile directories or regular contexts

## Code Examples

### Dynamic User Agent Fix
```javascript
// Get default UA and fix it dynamically
const tempContext = await browser.newContext();
const tempPage = await tempContext.newPage();
const defaultUA = await page.evaluate(() => navigator.userAgent);
await tempContext.close();

const fixedUA = defaultUA.replace('HeadlessChrome', 'Chrome');
```

### Comprehensive Evasion
```javascript
await page.addInitScript(() => {
  // Remove webdriver
  Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined
  });
  
  // Add Chrome object
  window.chrome = {
    runtime: {},
  };
  
  // Fix permissions
  const originalQuery = window.navigator.permissions.query;
  window.navigator.permissions.query = (parameters) => (
    parameters.name === 'notifications' ?
      Promise.resolve({ state: Notification.permission }) :
      originalQuery(parameters)
  );
});
```

## Testing Screenshots

- **vimeo-headless-screenshot.png** - Proof that Vimeo works in headless mode with our fix

## References

- Chrome's new headless mode: https://developer.chrome.com/docs/chromium/new-headless
- Playwright browsers documentation: https://playwright.dev/docs/browsers
- Bot detection test: https://bot.sannysoft.com/
- Headless detection test: https://arh.antoinevastel.com/bots/areyouheadless

## Summary

For most sites, simply changing the user agent is enough. For more sophisticated detection, use the standard protection approach. Only resort to playwright-extra-plugin-stealth for the most advanced anti-bot systems.

**The key discovery:** Many sites (like Vimeo) only check if the user agent contains "HeadlessChrome". Simply replacing this with "Chrome" is often all you need.

Remember: The goal is to use the minimal solution that works for your specific use case.