const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({
    channel: 'chrome',
    headless: true
  });

  const context = await browser.newContext({
    // The ONLY thing needed - remove "Headless" from user agent
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    viewport: { width: 1920, height: 1080 }  // Set a good size for screenshot
  });
  
  const page = await context.newPage();
  await page.goto('https://player.vimeo.com/video/887967850?background=1');
  await page.waitForLoadState('networkidle');
  
  // Wait a bit for video to load
  await page.waitForTimeout(3000);
  
  // Take screenshot
  await page.screenshot({ 
    path: 'vimeo-headless-screenshot.png',
    fullPage: false  // Just viewport
  });
  
  console.log('Screenshot saved as: vimeo-headless-screenshot.png');
  
  // Also check if video loaded successfully
  const hasError = await page.textContent('body').then(text => text.includes('Player error'));
  console.log('Video status:', hasError ? 'Error ❌' : 'Success ✅');
  
  await browser.close();
})();