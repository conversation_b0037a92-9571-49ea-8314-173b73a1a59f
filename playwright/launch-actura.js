const { chromium } = require('playwright');
const path = require('path');
const os = require('os');

(async () => {
  // Use a different profile directory to avoid conflicts
  const cacheDirectory = path.join(os.homedir(), 'Library', 'Caches');
  const userDataDir = path.join(cacheDirectory, 'ms-playwright', 'test-chrome-profile');
  
  console.log('Using user data directory:', userDataDir);
  
  // Launch with persistent context (same as playwright-mcp)
  const context = await chromium.launchPersistentContext(userDataDir, {
    // Launch options from playwright-mcp
    channel: 'chrome',
    headless: false,
    assistantMode: true,
    handleSIGINT: false,
    handleSIGTERM: false,
    chromiumSandbox: true,
    args: ['--start-maximized'],
    
    // Context options
    viewport: null
  });
  
  const page = context.pages()[0] || await context.newPage();
  await page.goto('https://player.vimeo.com/video/887967850?background=1');
  await page.waitForLoadState('networkidle');
  
  console.log('Page loaded successfully');
  
  // Wait for video player to initialize
  await page.waitForTimeout(3000);
  
  // Get all visible text using innerText
  const visibleText = await page.evaluate(() => {
    return document.body.innerText;
  });
  console.log('\nVisible text on page:');
  console.log(visibleText);
  
  // Try to get specific video player elements
  const playerInfo = await page.evaluate(() => {
    const info = {};
    
    // Check for video title
    const titleEl = document.querySelector('.vp-title, [class*="title"]');
    if (titleEl) info.title = titleEl.innerText;
    
    // Check for any overlay text
    const overlays = document.querySelectorAll('[class*="overlay"], [class*="text"], [class*="caption"]');
    info.overlayTexts = Array.from(overlays).map(el => el.innerText).filter(text => text);
    
    // Get all visible text elements
    const allTexts = Array.from(document.querySelectorAll('*')).map(el => {
      const style = window.getComputedStyle(el);
      if (style.display !== 'none' && style.visibility !== 'hidden' && el.innerText) {
        return el.innerText.trim();
      }
    }).filter(text => text && text.length > 0);
    
    info.allVisibleTexts = [...new Set(allTexts)]; // Remove duplicates
    
    return info;
  });
  
  console.log('\nPlayer information:', JSON.stringify(playerInfo, null, 2));
  
  // Take a screenshot to see what's actually displayed
  await page.screenshot({ path: 'vimeo-player-screenshot.png' });
  console.log('\nScreenshot saved as vimeo-player-screenshot.png');
  
  // Keep browser open for 10 seconds to view the page
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  await context.close();
})();