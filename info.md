Google Chrome	138.0.7204.169 (Official Build) (arm64) 
Revision	f67687ed433da666b8d134ca10de067bcc94c6fe-refs/branch-heads/7204@{#2082}
OS	macOS Version 15.5 (Build 24F74)
JavaScript	V8 13.8.258.29
User Agent	Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Command Line	/Applications/Google Chrome.app/Contents/MacOS/Google Chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AvoidUnnecessaryBeforeUnloadCheckSync,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,ExtensionManifestV2Unsupported,AutomationControlled --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-unsafe-swiftshader --user-data-dir=/Users/<USER>/Library/Caches/ms-playwright/mcp-chrome-profile --remote-debugging-port=54282 --flag-switches-begin --flag-switches-end about:blank
Executable Path	/Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Profile Path	/Users/<USER>/Library/Caches/ms-playwright/mcp-chrome-profile/Default
Linker	lld
Variations Seed Type	Null
Command-line Variations	eyJkaXNhYmxlLWZlYXR1cmVzIjoiQWNjZXB0Q0hGcmFtZSxBdXRvbWF0aW9u...	
Active Variations	b6f29041-ca7d8d80
b357b792-dcf2ae30
f4f00e05-ca7d8d80
9481ce98-3d47f4f4
a983f698-4a95a5b1
2a426c03-3d47f4f4
70678518-83ffdacd
be338734-4866ef6e
5f9907a9-206f6a6e
8eeccb9a-c35b209e
2b465683-206f6a6e
52fc7926-ee3d6169
bc9b361d-dee66fa8
a41a7188-b184655b
ff71bfdc-dee66fa8
e7cc79d5-dee66fa8
4b935545-3d47f4f4
9a38bae3-6046c8a7
2d1e43a3-3d47f4f4
386dc267-3d47f4f4
d69d967d-9cce7190
a4406b35-1657e2d6
408da146-1657e2d6